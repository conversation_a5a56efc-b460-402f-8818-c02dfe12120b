@import (reference) "~dingtalk-theme/dingtalk-x/mob.less";

.background-selector {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 12px;

  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
    gap: 10px;
  }

  .background-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all @common_light_motion_duration @common_light_motion_timing_function;

    &:hover:not(.disabled) {
      transform: translateY(-2px);
    }

    &.selected {
      .background-preview {
        border-color: @theme_primary1_color;
        box-shadow: 0 0 0 2px @theme_primary3_color;
      }

      .background-name {
        color: @theme_primary1_color;
        font-weight: 600;
      }
    }

    &.disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }

    .background-preview {
      width: 60px;
      height: 60px;
      border: 2px solid @common_line_light_color;
      border-radius: @common_border_radius_m;
      position: relative;
      overflow: hidden;
      transition: all @common_light_motion_duration @common_light_motion_timing_function;

      @media (max-width: 768px) {
        width: 50px;
        height: 50px;
      }

      &.none-preview {
        background: repeating-conic-gradient(#f0f0f0 0% 25%, transparent 0% 50%) 50% / 10px 10px;
        display: flex;
        align-items: center;
        justify-content: center;

        .none-text {
          .common_footnote_text_style_mob();
          color: @common_level3_base_color;
          font-size: 10px;
          text-align: center;
        }
      }

      .selected-indicator {
        position: absolute;
        top: 4px;
        right: 4px;
        width: 16px;
        height: 16px;
        background-color: @theme_primary1_color;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: @common_white1_color;
        font-size: 10px;
        z-index: 1;

        @media (max-width: 768px) {
          width: 14px;
          height: 14px;
          font-size: 8px;
        }
      }
    }

    .background-name {
      margin-top: 8px;
      .common_footnote_text_style_mob();
      color: @common_level2_base_color;
      text-align: center;
      transition: all @common_light_motion_duration @common_light_motion_timing_function;

      @media (max-width: 768px) {
        margin-top: 6px;
        font-size: 10px;
      }
    }
  }

  &.disabled {
    .background-option {
      cursor: not-allowed;
      
      &:hover {
        transform: none;
      }
    }
  }
}
