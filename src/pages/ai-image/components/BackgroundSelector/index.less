@import (reference) "~dingtalk-theme/dingtalk-x/mob.less";

.background-selector {
  // PC端：垂直排列 - PC: vertical layout
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  overflow-x: auto;
  overflow-y: visible; // 允许垂直方向溢出以显示放大效果 - Allow vertical overflow for scale effect
  gap: 8px;
  padding: 4px; // 为放大效果和滚动条留出空间 - Space for scale effect and scrollbar

  // 滚动条样式优化 - Optimized scrollbar styles
  &::-webkit-scrollbar {
    height: 4px; // 稍微增加高度便于交互 - Slightly increase height for better interaction
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.15); // 默认状态更透明 - More transparent in default state
    border-radius: 2px;
    opacity: 0; // 默认隐藏 - Hidden by default
    transition: opacity 0.3s ease-in-out; // 渐变过渡效果 - Fade transition effect
  }

  // 鼠标悬停时显示滚动条 - Show scrollbar on hover
  &:hover,
  &.scrolling {
    &::-webkit-scrollbar-thumb {
      opacity: 1; // 显示滚动条 - Show scrollbar
      background: rgba(0, 0, 0, 0.3); // 悬停时更明显 - More visible on hover
    }
  }

  // 直接悬停滚动条时的样式 - Style when directly hovering scrollbar
  &::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.5) !important; // 直接悬停滚动条时更深色 - Darker when directly hovering scrollbar
  }

  // 滚动条轨道悬停效果 - Scrollbar track hover effect
  &::-webkit-scrollbar-track:hover {
    background: rgba(0, 0, 0, 0.05); // 轨道悬停时的背景 - Track background on hover
  }

  .background-option {
    width: 72px;
    height: 72px;
    cursor: pointer;
    transition: all @common_light_motion_duration @common_light_motion_timing_function;
    overflow: visible; // 允许放大效果溢出 - Allow scale effect to overflow
    z-index: 1; // 确保悬停时在其他元素之上 - Ensure it's above other elements when hovered

    // 移动端：防止收缩 - Mobile: prevent shrinking
    @media (max-width: 768px) {
      flex-shrink: 0;
    }

    &:hover:not(.disabled) {
      transform: scale(1.05); // 轻微放大效果 - Slight scale effect
      z-index: 10; // 悬停时提升层级 - Increase z-index on hover
    }

    // 选中状态样式由内部图标处理 - Selected state handled by internal icon

    &.disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }

    .background-preview {
      width: 72px; // 固定尺寸 72px - Fixed size 72px
      height: 72px;
      border: none; // 移除边框 - Remove border
      border-radius: 8px; // 圆角 8px - Border radius 8px
      position: relative;
      overflow: hidden;
      transition: all @common_light_motion_duration @common_light_motion_timing_function;

      .selected-indicator {
        position: absolute;
        top: 6px; // 调整位置 - Adjust position
        right: 6px;
        width: 20px; // 稍大的图标 - Slightly larger icon
        height: 20px;
        background-color: @theme_primary1_color;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: @common_white1_color;
        font-size: 12px; // 图标大小 - Icon size
        z-index: 1;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); // 添加阴影增强可见性 - Add shadow for better visibility
      }
    }
  }

  &.disabled {
    .background-option {
      cursor: not-allowed;

      &:hover {
        transform: none;
      }
    }
  }
}
