@import (reference) "~dingtalk-theme/dingtalk-x/mob.less";

.background-selector {
  // PC端：垂直排列 - PC: vertical layout
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  overflow-x: auto;
  overflow-y: hidden;
  gap: 8px;
  padding-bottom: 4px; // 为滚动条留出空间 - Space for scrollbar

  // 隐藏滚动条但保持滚动功能 - Hide scrollbar but keep scrolling
  &::-webkit-scrollbar {
    height: 2px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 1px;
  }

  .background-option {
    width: 72px;
    height: 72px;
    cursor: pointer;
    transition: all @common_light_motion_duration @common_light_motion_timing_function;

    // 移动端：防止收缩 - Mobile: prevent shrinking
    @media (max-width: 768px) {
      flex-shrink: 0;
    }

    &:hover:not(.disabled) {
      transform: scale(1.03); // 轻微放大效果 - Slight scale effect
    }

    // 选中状态样式由内部图标处理 - Selected state handled by internal icon

    &.disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }

    .background-preview {
      width: 72px; // 固定尺寸 72px - Fixed size 72px
      height: 72px;
      border: none; // 移除边框 - Remove border
      border-radius: 8px; // 圆角 8px - Border radius 8px
      position: relative;
      overflow: hidden;
      transition: all @common_light_motion_duration @common_light_motion_timing_function;

      .selected-indicator {
        position: absolute;
        top: 6px; // 调整位置 - Adjust position
        right: 6px;
        width: 20px; // 稍大的图标 - Slightly larger icon
        height: 20px;
        background-color: @theme_primary1_color;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: @common_white1_color;
        font-size: 12px; // 图标大小 - Icon size
        z-index: 1;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); // 添加阴影增强可见性 - Add shadow for better visibility
      }
    }
  }

  &.disabled {
    .background-option {
      cursor: not-allowed;

      &:hover {
        transform: none;
      }
    }
  }
}
