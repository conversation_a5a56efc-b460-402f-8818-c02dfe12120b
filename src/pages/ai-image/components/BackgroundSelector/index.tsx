import React from 'react';
import { AddToSFilled, CheckOutlined } from '@ali/ding-icons';
import './index.less';

interface BackgroundOption {
  id: string;
  name: string;
  type: 'solid' | 'gradient' | 'texture';
  preview: string; // CSS background value or image URL
  thumbnail?: string; // Thumbnail image URL for complex backgrounds
}

interface BackgroundSelectorProps {
  value: string | null;
  onChange: (background: string | null) => void;
  disabled?: boolean;
}

const BackgroundSelector: React.FC<BackgroundSelectorProps> = ({
  value,
  onChange,
  disabled = false,
}) => {
  // Predefined background options
  const backgroundOptions: BackgroundOption[] = [
    // Solid colors
    {
      id: 'white',
      name: '纯白',
      type: 'solid',
      preview: '#ffffff',
    },
    {
      id: 'black',
      name: '纯黑',
      type: 'solid',
      preview: '#000000',
    },
    {
      id: 'gray',
      name: '灰色',
      type: 'solid',
      preview: '#f5f5f5',
    },
    // Gradients
    {
      id: 'blue-gradient',
      name: '蓝色渐变',
      type: 'gradient',
      preview: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    },
    {
      id: 'sunset-gradient',
      name: '日落渐变',
      type: 'gradient',
      preview: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    },
    {
      id: 'ocean-gradient',
      name: '海洋渐变',
      type: 'gradient',
      preview: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    },
    // Textures (using placeholder patterns)
    {
      id: 'paper-texture',
      name: '纸质纹理',
      type: 'texture',
      preview: 'radial-gradient(circle at 1px 1px, rgba(0,0,0,.15) 1px, transparent 0)',
      thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjVGNUY1Ii8+CjxjaXJjbGUgY3g9IjIwIiBjeT0iMjAiIHI9IjEiIGZpbGw9IiNFMEUwRTAiLz4KPC9zdmc+',
    },
    {
      id: 'fabric-texture',
      name: '布料纹理',
      type: 'texture',
      preview: 'repeating-linear-gradient(45deg, transparent, transparent 2px, rgba(0,0,0,.1) 2px, rgba(0,0,0,.1) 4px)',
      thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRkFGQUZBIi8+CjxwYXRoIGQ9Ik0wIDBoNDBMMCA0MHoiIGZpbGw9InJnYmEoMCwwLDAsMC4wNSkiLz4KPC9zdmc+',
    },
  ];

  // Handle background selection
  const handleBackgroundSelect = (backgroundId: string | null) => {
    onChange(backgroundId);
  };

  // Handle custom background upload (placeholder for future implementation)
  const handleCustomUpload = () => {
    // TODO: Implement custom background upload
    console.log('Custom background upload - to be implemented');
  };

  // Render background preview
  const renderBackgroundPreview = (option: BackgroundOption) => {
    const isSelected = value === option.id;

    return (
      <div
        key={option.id}
        className={`background-option ${isSelected ? 'selected' : ''} ${disabled ? 'disabled' : ''}`}
        onClick={() => !disabled && handleBackgroundSelect(option.id)}
      >
        <div
          className="background-preview"
          style={{
            background: option.thumbnail ? `url(${option.thumbnail})` : option.preview,
            backgroundSize: option.type === 'texture' ? '20px 20px' : 'cover',
          }}
        >
          {isSelected && (
            <div className="selected-indicator">
              <CheckOutlined />
            </div>
          )}
        </div>
        <div className="background-name">{option.name}</div>
      </div>
    );
  };

  return (
    <div className={`background-selector ${disabled ? 'disabled' : ''}`}>
      {/* None option */}
      <div
        className={`background-option ${value === null ? 'selected' : ''} ${disabled ? 'disabled' : ''}`}
        onClick={() => !disabled && handleBackgroundSelect(null)}
      >
        <div className="background-preview none-preview">
          {value === null && (
            <div className="selected-indicator">
              <CheckOutlined />
            </div>
          )}
          <span className="none-text">无背景</span>
        </div>
        <div className="background-name">透明背景</div>
      </div>

      {/* Predefined backgrounds */}
      {backgroundOptions.map(renderBackgroundPreview)}

      {/* Custom upload option */}
      <div
        className={`background-option custom-option ${disabled ? 'disabled' : ''}`}
        onClick={() => !disabled && handleCustomUpload()}
      >
        <div className="background-preview custom-preview">
          <AddToSFilled className="upload-icon" />
        </div>
        <div className="background-name">自定义</div>
      </div>
    </div>
  );
};

export default BackgroundSelector;
