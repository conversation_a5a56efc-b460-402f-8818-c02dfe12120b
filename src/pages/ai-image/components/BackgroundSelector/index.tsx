import React from 'react';
import { CheckCircleFilled } from '@ali/ding-icons';
import './index.less';

interface BackgroundOption {
  id: string;
  type: 'texture';
  imageUrl: string; // 纹理图片URL
}

interface BackgroundSelectorProps {
  value: string | null;
  onChange: (background: string | null) => void;
  disabled?: boolean;
}

const BackgroundSelector: React.FC<BackgroundSelectorProps> = ({
  value,
  onChange,
  disabled = false,
}) => {
  // 纹理背景选项数据 - Texture background options data
  const backgroundOptions: BackgroundOption[] = [
    {
      id: 'texture-1',
      type: 'texture',
      imageUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzIiIGhlaWdodD0iNzIiIHZpZXdCb3g9IjAgMCA3MiA3MiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjcyIiBoZWlnaHQ9IjcyIiBmaWxsPSIjMkY0RjRGIi8+CjxjaXJjbGUgY3g9IjE4IiBjeT0iMTgiIHI9IjMiIGZpbGw9IiM0MDYwNjAiLz4KPGNpcmNsZSBjeD0iNTQiIGN5PSIxOCIgcj0iMyIgZmlsbD0iIzQwNjA2MCIvPgo8Y2lyY2xlIGN4PSIxOCIgY3k9IjU0IiByPSIzIiBmaWxsPSIjNDA2MDYwIi8+CjxjaXJjbGUgY3g9IjU0IiBjeT0iNTQiIHI9IjMiIGZpbGw9IiM0MDYwNjAiLz4KPC9zdmc+', // 深色纹理
    },
    {
      id: 'texture-2',
      type: 'texture',
      imageUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzIiIGhlaWdodD0iNzIiIHZpZXdCb3g9IjAgMCA3MiA3MiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjcyIiBoZWlnaHQ9IjcyIiBmaWxsPSIjRkZGRkZGIi8+CjxwYXRoIGQ9Ik0wIDBoNzJMNzIgNzJIMHoiIGZpbGw9InVybCgjZ3JhZGllbnQpIi8+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWRpZW50IiB4MT0iMCIgeTE9IjAiIHgyPSI3MiIgeTI9IjcyIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+CjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiNGNUY1RjUiLz4KPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjRTBFMEUwIi8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPC9zdmc+', // 白色渐变纹理
    },
    {
      id: 'texture-3',
      type: 'texture',
      imageUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzIiIGhlaWdodD0iNzIiIHZpZXdCb3g9IjAgMCA3MiA3MiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjcyIiBoZWlnaHQ9IjcyIiBmaWxsPSIjRjVGNUY1Ii8+CjxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIzNiIgaGVpZ2h0PSIzNiIgZmlsbD0iI0VBRUFFQSIvPgo8cmVjdCB4PSIzNiIgeT0iMzYiIHdpZHRoPSIzNiIgaGVpZ2h0PSIzNiIgZmlsbD0iI0VBRUFFQSIvPgo8L3N2Zz4=', // 棋盘纹理
    },
    {
      id: 'texture-4',
      type: 'texture',
      imageUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzIiIGhlaWdodD0iNzIiIHZpZXdCb3g9IjAgMCA3MiA3MiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjcyIiBoZWlnaHQ9IjcyIiBmaWxsPSIjRkZGOEVEIi8+CjxwYXRoIGQ9Ik0wIDBoNzJMNzIgNzJIMHoiIGZpbGw9InVybCgjZ3JhZGllbnQpIi8+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWRpZW50IiB4MT0iMCIgeTE9IjAiIHgyPSI3MiIgeTI9IjcyIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+CjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiNGRkY4RUQiLz4KPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjRkZFQ0Q2Ii8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPC9zdmc+', // 暖色木质纹理
    },
    {
      id: 'texture-5',
      type: 'texture',
      imageUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNzIiIGhlaWdodD0iNzIiIHZpZXdCb3g9IjAgMCA3MiA3MiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjcyIiBoZWlnaHQ9IjcyIiBmaWxsPSIjRTZGM0ZGIi8+CjxwYXRoIGQ9Ik0wIDBoNzJMNzIgNzJIMHoiIGZpbGw9InVybCgjZ3JhZGllbnQpIi8+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWRpZW50IiB4MT0iMCIgeTE9IjAiIHgyPSI3MiIgeTI9IjcyIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+CjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiNFNkYzRkYiLz4KPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjQ0NFN0ZGIi8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPC9zdmc+', // 蓝色冷色调纹理
    },
  ];

  // Handle background selection - 处理背景选择
  const handleBackgroundSelect = (backgroundId: string) => {
    // 仅支持单选，选中后不可取消 - Only single selection, cannot deselect
    onChange(backgroundId);
  };

  // Render background preview - 渲染背景预览
  const renderBackgroundPreview = (option: BackgroundOption) => {
    const isSelected = value === option.id;

    return (
      <div
        key={option.id}
        className={`background-option ${isSelected ? 'selected' : ''} ${disabled ? 'disabled' : ''}`}
        onClick={() => !disabled && handleBackgroundSelect(option.id)}
      >
        <div
          className="background-preview"
          style={{
            backgroundImage: `url(${option.imageUrl})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        >
          {isSelected && (
            <div className="selected-indicator">
              <CheckCircleFilled />
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className={`background-selector ${disabled ? 'disabled' : ''}`}>
      {/* 纹理背景选项 - Texture background options */}
      {backgroundOptions.map(renderBackgroundPreview)}
    </div>
  );
};

export default BackgroundSelector;
