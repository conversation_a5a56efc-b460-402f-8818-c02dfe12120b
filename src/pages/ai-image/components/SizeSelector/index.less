@import (reference) "~dingtalk-theme/dingtalk-x/mob.less";

.size-selector {
  display: flex;
  flex-direction: column;
  gap: 12px;

  .size-option {
    display: flex;
    align-items: center;
    padding: 16px;
    border: 1px solid @common_line_light_color;
    border-radius: @common_border_radius_m;
    background-color: @common_bg_z1_color;
    cursor: pointer;
    transition: all @common_light_motion_duration @common_light_motion_timing_function;

    &:hover:not(.disabled) {
      border-color: @theme_primary1_color;
      background-color: @theme_primary3_color;
    }

    &.selected {
      border-color: @theme_primary1_color;
      background-color: @theme_primary3_color;
      box-shadow: 0 0 0 1px @theme_primary1_color;

      .size-info .size-name {
        color: @theme_primary1_color;
        font-weight: 600;
      }

      .size-rectangle {
        border-color: @theme_primary1_color;
        background-color: @theme_primary2_color;
      }
    }

    &.disabled {
      cursor: not-allowed;
      opacity: 0.5;

      &:hover {
        border-color: @common_line_light_color;
        background-color: @common_bg_z1_color;
      }
    }

    .size-preview {
      flex-shrink: 0;
      margin-right: 16px;
      width: 60px;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;

      @media (max-width: 768px) {
        width: 50px;
        height: 50px;
        margin-right: 12px;
      }

      .size-rectangle {
        max-width: 100%;
        max-height: 100%;
        border: 2px solid @common_level3_base_color;
        background-color: @common_level4_base_color;
        position: relative;
        transition: all @common_light_motion_duration @common_light_motion_timing_function;

        .selected-indicator {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 16px;
          height: 16px;
          background-color: @theme_primary1_color;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: @common_white1_color;
          font-size: 10px;

          @media (max-width: 768px) {
            width: 14px;
            height: 14px;
            font-size: 8px;
          }
        }
      }
    }

    .size-info {
      flex: 1;
      min-width: 0;

      .size-name {
        .common_body_bold_text_style_mob();
        color: @common_level1_base_color;
        margin-bottom: 4px;
        transition: all @common_light_motion_duration @common_light_motion_timing_function;
      }

      .size-ratio {
        .common_footnote_text_style_mob();
        color: @common_level2_base_color;
        margin-bottom: 2px;
      }

      .size-description {
        .common_footnote_text_style_mob();
        color: @common_level3_base_color;
        margin-bottom: 4px;
      }

      .size-recommended {
        .common_footnote_text_style_mob();
        color: @theme_primary1_color;
        font-size: 11px;
        line-height: 1.3;

        @media (max-width: 768px) {
          font-size: 10px;
        }
      }
    }
  }

  &.disabled {
    .size-option {
      cursor: not-allowed;
      
      &:hover {
        border-color: @common_line_light_color;
        background-color: @common_bg_z1_color;
      }
    }
  }
}

// Dark mode support
:root[data-dingtalk-theme='dark'] {
  .size-selector {
    .size-option {
      background-color: @common_bg_z1_color;
      border-color: @common_line_light_color;

      &:hover:not(.disabled) {
        background-color: @theme_primary3_color;
      }

      &.selected {
        background-color: @theme_primary3_color;
      }

      .size-preview .size-rectangle {
        border-color: @common_level3_base_color;
        background-color: @common_level4_base_color;
      }

      .size-info {
        .size-name {
          color: @common_level1_base_color;
        }

        .size-ratio {
          color: @common_level2_base_color;
        }

        .size-description {
          color: @common_level3_base_color;
        }

        .size-recommended {
          color: @theme_primary1_color;
        }
      }
    }
  }
}
